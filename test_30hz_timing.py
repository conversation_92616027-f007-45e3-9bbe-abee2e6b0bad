#!/usr/bin/env python3
"""
测试30Hz频率的时间间隔计算
"""

def test_30hz_timing():
    """测试30Hz频率的时间间隔"""
    
    print("=== 30Hz频率时间间隔测试 ===\n")
    
    # 30Hz的理论时间间隔
    target_fps = 30
    theoretical_interval_ms = 1000 / target_fps  # 毫秒
    theoretical_interval_ns = theoretical_interval_ms * 1000000  # 纳秒
    
    print(f"30Hz理论时间间隔:")
    print(f"  每帧间隔: {theoretical_interval_ms:.2f} ms")
    print(f"  每帧间隔: {theoretical_interval_ns:.0f} ns")
    
    # 脚本中使用的时间间隔
    script_interval_ns = 33333333  # 脚本中设置的值
    script_interval_ms = script_interval_ns / 1000000
    
    print(f"\n脚本设置的时间间隔:")
    print(f"  每帧间隔: {script_interval_ms:.2f} ms")
    print(f"  每帧间隔: {script_interval_ns} ns")
    
    # 计算误差
    error_ns = abs(theoretical_interval_ns - script_interval_ns)
    error_percentage = (error_ns / theoretical_interval_ns) * 100
    
    print(f"\n误差分析:")
    print(f"  误差: {error_ns:.0f} ns")
    print(f"  误差百分比: {error_percentage:.4f}%")
    
    # 模拟几个时间戳
    start_timestamp = 2436782088745944
    print(f"\n时间戳示例 (起始: {start_timestamp}):")
    
    current_timestamp = start_timestamp
    for i in range(5):
        print(f"  帧 {i+1}: {current_timestamp}")
        current_timestamp += script_interval_ns

if __name__ == "__main__":
    test_30hz_timing()
